"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Download,
  Share2,
  Heart,
  Facebook,
  Instagram,
  Twitter,
  Copy,
  Check,
  Sparkles,
  Camera,
  Star,
  Eye,
  Grid3X3,
  List,
  Filter,
  MoreHorizontal,
  Palette,
  Clock,
  CheckCircle
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { useTranslations } from "next-intl";
import Image from "next/image";

// 增强的生成照片接口 - Enhanced generated photo interface
interface GeneratedPhoto {
  id: string;
  style: string;
  styleName?: string;
  imageUrl: string;
  originalPhoto?: string;
  createdAt: string;
  userId?: string | null;
  generationMode?: 'face_swap' | 'standard';
  faceSwapEnabled?: boolean;
  quality?: 'excellent' | 'good' | 'fair';
  metadata?: {
    prompt?: string;
    processingTime?: number;
    aiModel?: string;
  };
}

// 增强的结果页面组件
// Enhanced results page component with better photo gallery and download functionality
export default function ResultsPage() {
  // 状态管理 - State management
  const [generatedPhotos, setGeneratedPhotos] = useState<GeneratedPhoto[]>([]);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [copiedLinks, setCopiedLinks] = useState<string[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<GeneratedPhoto | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterStyle, setFilterStyle] = useState<string>('all');
  const [isDownloading, setIsDownloading] = useState<string[]>([]);
  const [showShareModal, setShowShareModal] = useState(false);
  const [uploadedPhoto, setUploadedPhoto] = useState<string | null>(null);

  // 引用和路由 - Refs and routing
  const router = useRouter();
  const params = useParams();
  const currentLocale = params.locale as string;
  const t = useTranslations("photoAI.results");
  const tStyles = useTranslations("photoAI.styles");

  // 收藏照片处理函数 - Favorite photo handler
  const toggleFavorite = useCallback((photoId: string) => {
    setFavorites(prev => {
      const newFavorites = prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId];

      // 保存到本地存储 - Save to local storage
      localStorage.setItem('favoritePhotos', JSON.stringify(newFavorites));
      return newFavorites;
    });
  }, []);

  // 下载照片处理函数 - Download photo handler
  const downloadPhoto = useCallback(async (photo: GeneratedPhoto) => {
    setIsDownloading(prev => [...prev, photo.id]);

    try {
      // 创建下载链接 - Create download link
      const link = document.createElement('a');
      link.href = photo.imageUrl;
      link.download = `wedding-photo-${photo.styleName || photo.style}-${Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`📥 Downloaded photo: ${photo.id}`);
    } catch (error) {
      console.error('❌ Download failed:', error);
    } finally {
      setIsDownloading(prev => prev.filter(id => id !== photo.id));
    }
  }, []);

  // 批量下载处理函数 - Batch download handler
  const downloadAllPhotos = useCallback(async () => {
    const photosToDownload = filterStyle === 'all'
      ? generatedPhotos
      : generatedPhotos.filter(photo => photo.style === filterStyle);

    for (const photo of photosToDownload) {
      await downloadPhoto(photo);
      // 添加延迟避免浏览器阻止多个下载 - Add delay to avoid browser blocking multiple downloads
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }, [generatedPhotos, filterStyle, downloadPhoto]);

  // 分享照片处理函数 - Share photo handler
  const sharePhoto = useCallback(async (photo: GeneratedPhoto, platform: string) => {
    const shareText = `看看我的AI婚纱照！使用Hera-Web生成的${photo.styleName || photo.style}风格婚纱照 ✨`;
    const shareUrl = window.location.href;

    try {
      switch (platform) {
        case 'copy':
          await navigator.clipboard.writeText(shareUrl);
          setCopiedLinks(prev => [...prev, photo.id]);
          setTimeout(() => {
            setCopiedLinks(prev => prev.filter(id => id !== photo.id));
          }, 2000);
          break;
        case 'facebook':
          window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`);
          break;
        case 'twitter':
          window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`);
          break;
        case 'instagram':
          // Instagram doesn't support direct sharing, copy link instead
          await navigator.clipboard.writeText(shareUrl);
          alert('链接已复制，请在Instagram中粘贴分享');
          break;
      }
    } catch (error) {
      console.error('❌ Share failed:', error);
    }
  }, []);

  // 获取过滤后的照片 - Get filtered photos
  const filteredPhotos = filterStyle === 'all'
    ? generatedPhotos
    : generatedPhotos.filter(photo => photo.style === filterStyle);

  // 获取可用的风格列表 - Get available styles list
  const availableStyles = Array.from(new Set(generatedPhotos.map(photo => photo.style)));

  useEffect(() => {
    console.log("📋 Results page: Checking session data");

    // 检查必需的数据 - Check if we have the required data
    const uploadedPhotoData = sessionStorage.getItem("uploadedPhoto");
    const selectedStylesStr = sessionStorage.getItem("selectedStyles");
    const generatedPhotosStr = sessionStorage.getItem("generatedPhotos");

    // 加载收藏列表 - Load favorites list
    const savedFavorites = localStorage.getItem('favoritePhotos');
    if (savedFavorites) {
      try {
        setFavorites(JSON.parse(savedFavorites));
      } catch (error) {
        console.error('❌ Error loading favorites:', error);
      }
    }

    console.log("📊 Session data status:", {
      hasPhoto: !!uploadedPhotoData,
      hasStyles: !!selectedStylesStr,
      hasGeneratedPhotos: !!generatedPhotosStr
    });

    if (!uploadedPhotoData || !selectedStylesStr) {
      console.log("❌ Missing required data, redirecting to upload");
      router.push(`/${currentLocale}/upload`);
      return;
    }

    // 设置上传的照片 - Set uploaded photo
    setUploadedPhoto(uploadedPhotoData);

    // 尝试从API调用获取生成的照片 - Try to get generated photos from API call first
    if (generatedPhotosStr) {
      try {
        const apiGeneratedPhotos = JSON.parse(generatedPhotosStr);
        console.log("✅ Using API generated photos:", apiGeneratedPhotos.length, "photos");
        setGeneratedPhotos(apiGeneratedPhotos);
        return;
      } catch (error) {
        console.error("❌ Error parsing generated photos:", error);
      }
    }

    // 如果没有API生成的照片，则回退到模拟照片 - Fallback to mock photos if no API generated photos
    console.log("⚠️ No API generated photos found, creating fallback photos");
    const selectedStyles = JSON.parse(selectedStylesStr);
    const styles = tStyles.raw("styles") as Array<{
      id: string;
      name: string;
      description: string;
    }>;

    const mockPhotos: GeneratedPhoto[] = selectedStyles.map((styleId: string, index: number) => {
      const style = styles.find(s => s.id === styleId);
      return {
        id: `photo-${Date.now()}-${index}`,
        style: styleId,
        styleName: style?.name || styleId,
        imageUrl: `/placeholder.svg?height=600&width=400&text=${encodeURIComponent(style?.name || styleId)}`,
        originalPhoto: uploadedPhotoData,
        createdAt: new Date().toISOString(),
        quality: 'excellent',
        generationMode: 'face_swap',
        metadata: {
          prompt: `Professional wedding photography: ${style?.description || styleId}`,
          processingTime: 120,
          aiModel: 'mock-dall-e-3'
        }
      };
    });

    setGeneratedPhotos(mockPhotos);
  }, [router, tStyles, currentLocale]);

  const toggleFavorite = (photoId: string) => {
    setFavorites(prev => 
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const handleDownload = async (photo: GeneratedPhoto) => {
    try {
      console.log("📥 Starting download for photo:", photo.id);

      // For OpenAI generated images, we need to fetch and download
      if (photo.imageUrl.startsWith('http')) {
        const response = await fetch(photo.imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `wedding-photo-${photo.style}-${photo.id}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the object URL
        window.URL.revokeObjectURL(url);
        console.log("✅ Download completed for photo:", photo.id);
      } else {
        // Fallback for placeholder images
        const link = document.createElement('a');
        link.href = photo.imageUrl;
        link.download = `wedding-photo-${photo.style}-${photo.id}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("❌ Error downloading photo:", error);
      alert("Failed to download image. Please try again.");
    }
  };

  const handleCopyLink = (photoId: string) => {
    const url = `${window.location.origin}/photo/${photoId}`;
    navigator.clipboard.writeText(url);
    setCopiedLinks(prev => [...prev, photoId]);
    setTimeout(() => {
      setCopiedLinks(prev => prev.filter(id => id !== photoId));
    }, 2000);
  };

  const handleShare = (platform: string, photo: GeneratedPhoto) => {
    const url = `${window.location.origin}/photo/${photo.id}`;
    const text = `Check out my AI-generated wedding photo in ${photo.style} style!`;
    
    let shareUrl = '';
    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        break;
      case 'instagram':
        // Instagram doesn't support direct URL sharing, so we'll copy to clipboard
        navigator.clipboard.writeText(`${text} ${url}`);
        alert('Link copied to clipboard! You can paste it in your Instagram post.');
        return;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  if (generatedPhotos.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">{t("noResults")}</p>
          <Link href={`/${currentLocale}/upload`}>
            <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white">
              {t("backToUpload")}
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 py-12">
      <div className="container mx-auto px-4">
        <Link href={`/${currentLocale}/upload`} className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> {t("backToUpload")}
        </Link>

        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold mb-4">{t("title")}</h1>
            <p className="text-gray-600 text-lg">{t("subtitle")}</p>
            <p className="text-gray-500 mt-2">{t("description")}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {generatedPhotos.map((photo) => (
              <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative">
                  <img
                    src={photo.imageUrl}
                    alt={`${photo.style} wedding photo`}
                    className="w-full h-80 object-cover"
                  />
                  <button
                    className={`absolute top-4 right-4 p-2 rounded-full transition-colors ${
                      favorites.includes(photo.id)
                        ? "bg-pink-500 text-white"
                        : "bg-white/80 text-gray-600 hover:bg-white"
                    }`}
                    onClick={() => toggleFavorite(photo.id)}
                  >
                    <Heart className="h-5 w-5" fill={favorites.includes(photo.id) ? "currentColor" : "none"} />
                  </button>
                </div>

                <div className="p-4">
                  <h3 className="font-bold mb-3">{photo.style}</h3>
                  
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleDownload(photo)}
                      className="flex-1 bg-pink-500 hover:bg-pink-600 text-white"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      {t("download")}
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button size="sm" variant="outline" className="flex-1">
                          <Share2 className="mr-2 h-4 w-4" />
                          {t("share")}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleShare('facebook', photo)}>
                          <Facebook className="mr-2 h-4 w-4" />
                          {t("facebook")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleShare('twitter', photo)}>
                          <Twitter className="mr-2 h-4 w-4" />
                          {t("twitter")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleShare('instagram', photo)}>
                          <Instagram className="mr-2 h-4 w-4" />
                          {t("instagram")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyLink(photo.id)}>
                          {copiedLinks.includes(photo.id) ? (
                            <>
                              <Check className="mr-2 h-4 w-4" />
                              {t("linkCopied")}
                            </>
                          ) : (
                            <>
                              <Copy className="mr-2 h-4 w-4" />
                              {t("copyLink")}
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="text-center space-y-6">
            <Button
              className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-8 py-6 rounded-xl text-lg font-medium"
              onClick={() => {
                router.push(`/${currentLocale}/upload`);
              }}
            >
              {t("createMore")}
            </Button>

            <p className="text-sm text-gray-500">
              {t("storageNote")}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
