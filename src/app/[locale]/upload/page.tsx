"use client";

import { useState, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, Upload, AlertCircle, CheckCircle, Camera, Image as ImageIcon, Sparkles } from "lucide-react";
import { useTranslations } from "next-intl";

// 增强的照片上传页面组件
// Enhanced photo upload page component with improved validation, preview, and user feedback
export default function UploadPage() {
  // 状态管理 - State management
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [photoQuality, setPhotoQuality] = useState<'excellent' | 'good' | 'fair' | 'poor' | null>(null);
  const [validationMessages, setValidationMessages] = useState<string[]>([]);

  // 引用和路由 - Refs and routing
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const t = useTranslations("photoAI.upload");

  // 照片质量评估函数 - Photo quality assessment function
  const assessPhotoQuality = useCallback((file: File, imageElement: HTMLImageElement) => {
    const messages: string[] = [];
    let quality: 'excellent' | 'good' | 'fair' | 'poor' = 'excellent';

    // 检查文件大小 - Check file size
    if (file.size < 100 * 1024) { // Less than 100KB
      messages.push("照片文件较小，可能影响生成质量");
      quality = 'poor';
    } else if (file.size < 500 * 1024) { // Less than 500KB
      messages.push("建议使用更高分辨率的照片");
      quality = 'fair';
    }

    // 检查图片尺寸 - Check image dimensions
    if (imageElement.naturalWidth < 512 || imageElement.naturalHeight < 512) {
      messages.push("照片分辨率较低，建议使用至少512x512像素的照片");
      quality = quality === 'excellent' ? 'fair' : quality;
    } else if (imageElement.naturalWidth >= 1024 && imageElement.naturalHeight >= 1024) {
      messages.push("照片分辨率优秀，将获得最佳生成效果");
    }

    // 检查宽高比 - Check aspect ratio
    const aspectRatio = imageElement.naturalWidth / imageElement.naturalHeight;
    if (aspectRatio < 0.7 || aspectRatio > 1.5) {
      messages.push("建议使用接近正方形的照片以获得最佳效果");
      quality = quality === 'excellent' ? 'good' : quality;
    }

    setPhotoQuality(quality);
    setValidationMessages(messages);
  }, []);

  // 文件选择处理函数 - File selection handler
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  // 增强的文件处理函数 - Enhanced file handling function
  const handleFile = useCallback((file: File) => {
    setIsProcessing(true);
    setUploadProgress(0);
    setError(null);
    setValidationMessages([]);
    setPhotoQuality(null);

    // 基础验证 - Basic validation
    if (!file.type.match("image.*")) {
      setError(t("uploadError"));
      setIsProcessing(false);
      return;
    }

    // 文件大小检查 (最大10MB) - File size check (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError(t("fileSizeError"));
      setIsProcessing(false);
      return;
    }

    // 支持的格式检查 - Supported format check
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'];
    if (!supportedFormats.includes(file.type.toLowerCase())) {
      setError("不支持的文件格式，请使用 JPEG、PNG、WebP 或 HEIC 格式");
      setIsProcessing(false);
      return;
    }

    setSelectedFile(file);

    // 创建预览并评估质量 - Create preview and assess quality
    const reader = new FileReader();
    reader.onprogress = (e) => {
      if (e.lengthComputable) {
        setUploadProgress((e.loaded / e.total) * 100);
      }
    };

    reader.onloadend = () => {
      const result = reader.result as string;
      setPreviewUrl(result);

      // 创建图片元素进行质量评估 - Create image element for quality assessment
      const img = new Image();
      img.onload = () => {
        assessPhotoQuality(file, img);
        setIsProcessing(false);
        setUploadProgress(100);
      };
      img.src = result;
    };

    reader.readAsDataURL(file);
  }, [t, assessPhotoQuality]);

  // 拖拽处理函数 - Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // 只有当离开整个拖拽区域时才设置为false - Only set false when leaving entire drop zone
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  }, [handleFile]);

  // 清除选择的文件 - Clear selected file
  const clearSelection = useCallback(() => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setError(null);
    setValidationMessages([]);
    setPhotoQuality(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  // 继续到风格选择页面 - Continue to style selection page
  const handleContinue = useCallback(() => {
    if (!selectedFile) {
      setError(t("selectFirst"));
      return;
    }

    // 检查照片质量警告 - Check photo quality warnings
    if (photoQuality === 'poor') {
      const confirmed = window.confirm(
        "检测到照片质量较低，可能影响生成效果。是否继续？\n建议使用更高质量的照片以获得最佳效果。"
      );
      if (!confirmed) return;
    }

    console.log("📸 New photo uploaded, clearing previous session data");
    setIsProcessing(true);

    // 清除之前的会话数据以开始新的流程 - Clear previous session data to start fresh
    sessionStorage.removeItem("selectedStyles");
    sessionStorage.removeItem("generatedPhotos");

    // 将文件存储到sessionStorage供下一页使用 - Store file in sessionStorage for next page
    const reader = new FileReader();
    reader.onloadend = () => {
      // 存储照片数据和元信息 - Store photo data and metadata
      const photoData = {
        imageData: reader.result as string,
        fileName: selectedFile.name,
        fileSize: selectedFile.size,
        fileType: selectedFile.type,
        quality: photoQuality,
        uploadTime: new Date().toISOString()
      };

      sessionStorage.setItem("uploadedPhoto", reader.result as string);
      sessionStorage.setItem("photoMetadata", JSON.stringify(photoData));

      // 获取当前语言环境并导航 - Get current locale and navigate
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/styles`);
    };
    reader.readAsDataURL(selectedFile);
  }, [selectedFile, photoQuality, t, router]);

  // 获取质量指示器颜色 - Get quality indicator color
  const getQualityColor = (quality: string | null) => {
    switch (quality) {
      case 'excellent': return 'text-green-600 bg-green-50 border-green-200';
      case 'good': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'fair': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'poor': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const currentLocale = window.location.pathname.split('/')[1];

  return (
    <div className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      {/* 背景装饰 - Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-white to-rose-50 opacity-60"></div>
      <div className="absolute top-20 left-10 w-32 h-32 bg-pink-200 rounded-full opacity-20 animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-24 h-24 bg-rose-200 rounded-full opacity-20 animate-pulse delay-1000"></div>

      <div className="container mx-auto px-4 relative z-10">
        <Link href={`/${currentLocale}`} className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8 transition-colors">
          <ArrowLeft className="mr-2 h-4 w-4" /> {t("backToHome")}
        </Link>

        <div className="max-w-3xl mx-auto">
          {/* 页面标题 - Page header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full mr-4">
                <Camera className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                {t("title")}
              </h1>
            </div>
            <p className="text-gray-600 text-lg mb-2">{t("subtitle")}</p>
            <p className="text-gray-500">{t("description")}</p>
          </div>

          {/* 主上传卡片 - Main upload card */}
          <Card className="p-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <div
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                isDragging
                  ? "border-pink-500 bg-pink-50 scale-105 shadow-lg"
                  : previewUrl
                  ? "border-green-500 bg-green-50"
                  : "border-gray-300 hover:border-pink-400 hover:bg-pink-25"
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {previewUrl ? (
                <div className="space-y-6">
                  {/* 照片预览 - Photo preview */}
                  <div className="relative inline-block">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="max-w-full max-h-80 mx-auto rounded-lg shadow-lg border-2 border-white"
                    />
                    {isProcessing && (
                      <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                        <div className="bg-white rounded-full p-3">
                          <Sparkles className="h-6 w-6 text-pink-500 animate-spin" />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 文件信息 - File info */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <p className="text-green-600 font-medium">
                        {selectedFile?.name}
                      </p>
                    </div>

                    {/* 上传进度 - Upload progress */}
                    {isProcessing && (
                      <div className="space-y-2">
                        <Progress value={uploadProgress} className="w-full" />
                        <p className="text-sm text-gray-500">处理中... {Math.round(uploadProgress)}%</p>
                      </div>
                    )}

                    {/* 照片质量指示器 - Photo quality indicator */}
                    {photoQuality && !isProcessing && (
                      <div className={`inline-flex items-center px-3 py-2 rounded-full border text-sm font-medium ${getQualityColor(photoQuality)}`}>
                        <Sparkles className="h-4 w-4 mr-2" />
                        照片质量: {photoQuality === 'excellent' ? '优秀' :
                                  photoQuality === 'good' ? '良好' :
                                  photoQuality === 'fair' ? '一般' : '较差'}
                      </div>
                    )}

                    {/* 文件详情 - File details */}
                    {selectedFile && !isProcessing && (
                      <div className="text-sm text-gray-500 space-y-1">
                        <p>大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                        <p>格式: {selectedFile.type}</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* 上传图标和动画 - Upload icon and animation */}
                  <div className="relative">
                    <div className={`mx-auto w-24 h-24 rounded-full bg-gradient-to-r from-pink-100 to-rose-100 flex items-center justify-center transition-transform duration-300 ${isDragging ? 'scale-110' : ''}`}>
                      <Upload className={`h-12 w-12 text-pink-500 transition-transform duration-300 ${isDragging ? 'scale-110' : ''}`} />
                    </div>
                    {isDragging && (
                      <div className="absolute inset-0 rounded-full border-4 border-pink-300 border-dashed animate-spin"></div>
                    )}
                  </div>

                  {/* 上传说明 - Upload instructions */}
                  <div className="space-y-3">
                    <p className="text-xl font-semibold text-gray-900">
                      {isDragging ? "松开以上传照片" : t("dragDrop")}
                    </p>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-600">{t("supportedFormats")}</p>
                      <p className="text-sm text-gray-600">{t("maxSize")}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* 隐藏的文件输入 - Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                id="file-upload"
                className="hidden"
                accept="image/jpeg,image/jpg,image/png,image/webp,image/heic"
                onChange={handleFileChange}
              />

              {/* 操作按钮 - Action buttons */}
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  className="border-pink-500 text-pink-500 hover:bg-pink-50 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                >
                  <ImageIcon className="mr-2 h-4 w-4" />
                  {previewUrl ? t("chooseAnother") : t("selectPhoto")}
                </Button>

                {previewUrl && (
                  <Button
                    variant="ghost"
                    className="text-gray-500 hover:text-gray-700"
                    onClick={clearSelection}
                    disabled={isProcessing}
                  >
                    清除选择
                  </Button>
                )}
              </div>
            </div>

            {/* 错误信息显示 - Error message display */}
            {error && (
              <div className="mt-6 p-4 bg-red-50 text-red-700 rounded-lg flex items-start border border-red-200">
                <AlertCircle className="h-5 w-5 mr-3 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">上传失败</p>
                  <p className="text-sm mt-1">{error}</p>
                </div>
              </div>
            )}

            {/* 验证消息显示 - Validation messages display */}
            {validationMessages.length > 0 && !error && (
              <div className="mt-6 space-y-2">
                {validationMessages.map((message, index) => (
                  <div key={index} className={`p-3 rounded-lg text-sm flex items-start ${
                    photoQuality === 'excellent' ? 'bg-green-50 text-green-700 border border-green-200' :
                    photoQuality === 'good' ? 'bg-blue-50 text-blue-700 border border-blue-200' :
                    photoQuality === 'fair' ? 'bg-yellow-50 text-yellow-700 border border-yellow-200' :
                    'bg-red-50 text-red-700 border border-red-200'
                  }`}>
                    <Sparkles className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                    <span>{message}</span>
                  </div>
                ))}
              </div>
            )}

            {/* 继续按钮 - Continue button */}
            <div className="mt-8">
              <Button
                className="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white py-6 rounded-xl text-lg font-medium transition-all duration-300 transform hover:scale-105 disabled:transform-none disabled:opacity-50"
                disabled={!selectedFile || isProcessing}
                onClick={handleContinue}
              >
                {isProcessing ? (
                  <div className="flex items-center justify-center">
                    <Sparkles className="h-5 w-5 mr-2 animate-spin" />
                    处理中...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <Sparkles className="h-5 w-5 mr-2" />
                    {t("continue")}
                  </div>
                )}
              </Button>
            </div>
          </Card>

          {/* 使用提示和隐私说明 - Usage tips and privacy notice */}
          <div className="mt-8 space-y-6">
            {/* 使用提示 - Usage tips */}
            <Card className="p-6 bg-gradient-to-r from-pink-50 to-rose-50 border-pink-200">
              <h3 className="font-semibold text-pink-800 mb-3 flex items-center">
                <Camera className="h-5 w-5 mr-2" />
                拍照小贴士
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-pink-700">
                <div className="flex items-start">
                  <CheckCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>使用充足的自然光线</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>保持面部清晰可见</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>选择简洁的背景</span>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>正面拍摄效果最佳</span>
                </div>
              </div>
            </Card>

            {/* 隐私说明 - Privacy notice */}
            <div className="text-center text-sm text-gray-500 space-y-2">
              <p className="flex items-center justify-center">
                <span className="mr-2">🔒</span>
                {t("privacyNote")}
              </p>
              <p className="flex items-center justify-center">
                <span className="mr-2">⏰</span>
                {t("securityNote")}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
