"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Sparkles, X, Camera, Clock, CheckCircle, AlertCircle, Palette, Heart } from "lucide-react";
import { useTranslations } from "next-intl";

// 增强的生成进度页面组件
// Enhanced generation progress page component with better progress tracking
export default function GeneratingPage() {
  // 状态管理 - State management
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStyle, setCurrentStyle] = useState<string>('');
  const [completedStyles, setCompletedStyles] = useState<string[]>([]);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number>(0);
  const [startTime, setStartTime] = useState<number>(0);
  const [selectedStylesData, setSelectedStylesData] = useState<any>(null);

  // 引用和路由 - Refs and routing
  const router = useRouter();
  const t = useTranslations("photoAI.generating");
  const apiCallMadeRef = useRef(false);
  const isComponentMountedRef = useRef(true);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 生成步骤定义 - Generation steps definition
  const generationSteps = [
    { id: 'analyzing', name: '分析照片', icon: Camera, duration: 15 },
    { id: 'processing', name: '处理风格', icon: Palette, duration: 60 },
    { id: 'generating', name: '生成婚纱照', icon: Sparkles, duration: 20 },
    { id: 'finalizing', name: '完成处理', icon: CheckCircle, duration: 5 }
  ];

  // 智能进度模拟函数 - Smart progress simulation function
  const simulateProgress = useCallback(() => {
    if (!selectedStylesData) return;

    const totalStyles = selectedStylesData.selectedStyles.length;
    const totalSteps = generationSteps.length;
    const totalDuration = generationSteps.reduce((sum, step) => sum + step.duration, 0) * totalStyles;

    let currentProgress = 0;
    let currentStepIndex = 0;
    let currentStyleIndex = 0;

    const interval = setInterval(() => {
      if (!isComponentMountedRef.current) {
        clearInterval(interval);
        return;
      }

      currentProgress += 1;
      const progressPercentage = Math.min((currentProgress / totalDuration) * 100, 95);

      // 更新当前步骤 - Update current step
      const stepProgress = currentProgress % generationSteps.reduce((sum, step) => sum + step.duration, 0);
      let accumulatedDuration = 0;
      for (let i = 0; i < generationSteps.length; i++) {
        accumulatedDuration += generationSteps[i].duration;
        if (stepProgress <= accumulatedDuration) {
          setCurrentStep(i);
          break;
        }
      }

      // 更新当前风格 - Update current style
      const styleIndex = Math.floor(currentProgress / generationSteps.reduce((sum, step) => sum + step.duration, 0));
      if (styleIndex < totalStyles && styleIndex !== currentStyleIndex) {
        currentStyleIndex = styleIndex;
        setCurrentStyle(selectedStylesData.selectedStyles[styleIndex]);

        // 添加到已完成列表 - Add to completed list
        if (styleIndex > 0) {
          setCompletedStyles(prev => [...prev, selectedStylesData.selectedStyles[styleIndex - 1]]);
        }
      }

      // 更新预估剩余时间 - Update estimated remaining time
      const remainingTime = Math.max(0, totalDuration - currentProgress);
      setEstimatedTimeRemaining(remainingTime);

      setProgress(progressPercentage);

      // 如果进度达到95%，停止模拟等待API响应 - Stop simulation at 95% to wait for API response
      if (progressPercentage >= 95) {
        clearInterval(interval);
      }
    }, 1000);

    progressIntervalRef.current = interval;
  }, [selectedStylesData, generationSteps]);

  // API调用函数 - API call function
  const generatePhotos = useCallback(async (photoUrl: string, styles: string[]) => {
    // 防止重复调用 - Prevent duplicate calls
    if (isGenerating || apiCallMadeRef.current) {
      console.log("⚠️ Generation already in progress or completed, skipping duplicate call", {
        isGenerating,
        apiCallMade: apiCallMadeRef.current
      });
      return;
    }

    try {
      console.log("🚀 Starting photo generation with styles:", styles);
      console.log("📋 Request details:", {
        photoUrl: photoUrl ? `${photoUrl.substring(0, 50)}...` : "missing",
        stylesCount: styles.length,
        stylesArray: styles,
        timestamp: new Date().toISOString()
      });

      // 标记API调用已开始 - Mark API call as started
      apiCallMadeRef.current = true;
      setIsGenerating(true);
      setError(null);
      setStartTime(Date.now());

      const requestBody = {
        photoUrl,
        styles,
        userId: null // You can add user ID if available
      };

      console.log("📤 Sending request to /api/generate:", requestBody);

      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log("📥 Received response:", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      const data = await response.json();
      console.log("📄 Response data:", data);

      if (!response.ok) {
        console.error("❌ API Error:", data);
        throw new Error(data.error || 'Failed to generate photos');
      }

      console.log("✅ Generation completed successfully:", {
        success: data.success,
        photosCount: data.photos?.length || 0,
        jobId: data.jobId
      });

      // Validate response data
      if (!data.photos || !Array.isArray(data.photos) || data.photos.length === 0) {
        console.error("❌ Invalid response: No photos generated");
        throw new Error('No photos were generated');
      }

      // 完成进度并存储生成的照片 - Complete progress and store generated photos
      setProgress(100);
      setCurrentStep(generationSteps.length - 1);
      setCompletedStyles(styles);

      // 清除进度模拟 - Clear progress simulation
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }

      sessionStorage.setItem("generatedPhotos", JSON.stringify(data.photos));
      console.log("💾 Stored generated photos in sessionStorage");

      // 短暂延迟后导航到结果页面 - Navigate to results page after brief delay
      setTimeout(() => {
        const currentLocale = window.location.pathname.split('/')[1];
        console.log("🔄 Navigating to results page:", `/${currentLocale}/results`);
        router.push(`/${currentLocale}/results`);
      }, 1500);

    } catch (error) {
      console.error("❌ Error generating photos:", error);
      console.error("❌ Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });

      // 清除进度模拟 - Clear progress simulation
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }

      // 重置API调用标志以允许重试 - Reset API call flag on error to allow retry
      apiCallMadeRef.current = false;
      setError(error instanceof Error ? error.message : 'Failed to generate photos');
      setIsGenerating(false);
      setProgress(0);
      setCurrentStep(0);
    }
  }, [router, generationSteps]);

  useEffect(() => {
    // 防止重复执行 - Prevent duplicate effect runs
    if (apiCallMadeRef.current) {
      console.log("⚠️ API call already made, skipping useEffect");
      return;
    }

    console.log("🔄 useEffect running, checking session data...");

    // 检查必需的数据 - Check if we have the required data
    const uploadedPhoto = sessionStorage.getItem("uploadedPhoto");
    const selectedStylesStr = sessionStorage.getItem("selectedStyles");
    const styleSelectionDataStr = sessionStorage.getItem("styleSelectionData");
    const generatedPhotosStr = sessionStorage.getItem("generatedPhotos");

    console.log("📋 Session data check:", {
      hasPhoto: !!uploadedPhoto,
      hasStyles: !!selectedStylesStr,
      hasStyleData: !!styleSelectionDataStr,
      hasGeneratedPhotos: !!generatedPhotosStr,
      photoLength: uploadedPhoto?.length || 0,
      stylesData: selectedStylesStr
    });

    if (!uploadedPhoto || !selectedStylesStr) {
      console.log("❌ Missing required data, redirecting to upload");
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/upload`);
      return;
    }

    // 如果照片已经生成，跳过API调用直接跳转到结果页面 - If photos already generated, skip API call and go to results
    if (generatedPhotosStr) {
      console.log("✅ Photos already generated, redirecting to results");
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/results`);
      return;
    }

    // 解析风格选择数据 - Parse style selection data
    if (styleSelectionDataStr) {
      try {
        const styleData = JSON.parse(styleSelectionDataStr);
        setSelectedStylesData(styleData);
        console.log("🎨 Loaded style selection data:", styleData);
      } catch (error) {
        console.error("❌ Error parsing style selection data:", error);
      }
    }

    let selectedStyles: string[];
    try {
      selectedStyles = JSON.parse(selectedStylesStr);
      console.log("🎨 Parsed selected styles:", selectedStyles);

      if (!Array.isArray(selectedStyles) || selectedStyles.length === 0) {
        console.error("❌ Invalid styles data:", selectedStyles);
        const currentLocale = window.location.pathname.split('/')[1];
        router.push(`/${currentLocale}/styles`);
        return;
      }
    } catch (error) {
      console.error("❌ Error parsing styles:", error);
      const currentLocale = window.location.pathname.split('/')[1];
      router.push(`/${currentLocale}/styles`);
      return;
    }

    // 防止多次API调用 - Prevent multiple API calls by checking if already generating
    if (isGenerating || apiCallMadeRef.current) {
      console.log("⚠️ Already generating or API call made, skipping duplicate call");
      return;
    }

    console.log("🚀 Starting generation process...");

    // 开始智能进度模拟 - Start smart progress simulation
    simulateProgress();

    // 调用真实的API - Call the real API
    generatePhotos(uploadedPhoto, selectedStyles);

    return () => {
      // 清理函数 - Cleanup function
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
      isComponentMountedRef.current = false;
    };
  }, [simulateProgress, generatePhotos]); // 依赖项数组 - Dependencies array

  // 取消生成处理函数 - Cancel generation handler
  const handleCancel = useCallback(() => {
    // 清除进度模拟 - Clear progress simulation
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    // 清除会话存储并重定向 - Clear session storage and redirect
    sessionStorage.removeItem("uploadedPhoto");
    sessionStorage.removeItem("selectedStyles");
    sessionStorage.removeItem("styleSelectionData");
    sessionStorage.removeItem("generatedPhotos");

    const currentLocale = window.location.pathname.split('/')[1];
    router.push(`/${currentLocale}/upload`);
  }, [router]);

  // 重试生成处理函数 - Retry generation handler
  const handleRetry = useCallback(() => {
    console.log("🔄 Retrying generation, clearing old results");

    // 清除进度模拟 - Clear progress simulation
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    // 清除旧结果 - Clear old results before retrying
    sessionStorage.removeItem("generatedPhotos");

    // 重置所有状态和引用 - Reset all states and refs
    setError(null);
    setProgress(0);
    setCurrentStep(0);
    setCurrentStyle('');
    setCompletedStyles([]);
    setEstimatedTimeRemaining(0);
    setIsGenerating(false);
    apiCallMadeRef.current = false; // 重置API调用标志 - Reset API call flag

    const uploadedPhoto = sessionStorage.getItem("uploadedPhoto");
    const selectedStylesStr = sessionStorage.getItem("selectedStyles");

    if (uploadedPhoto && selectedStylesStr) {
      try {
        const selectedStyles = JSON.parse(selectedStylesStr);
        console.log("🔄 Retrying with styles:", selectedStyles);

        // 重新开始进度模拟 - Restart progress simulation
        simulateProgress();
        generatePhotos(uploadedPhoto, selectedStyles);
      } catch (error) {
        console.error("❌ Error parsing styles for retry:", error);
        setError("Invalid styles data");
      }
    } else {
      console.error("❌ Missing data for retry");
      setError("Missing required data for retry");
    }
  }, [simulateProgress, generatePhotos]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-white via-pink-25 to-rose-50 flex items-center justify-center">
      {/* 背景装饰 - Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-pink-200 rounded-full opacity-10 animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-rose-200 rounded-full opacity-10 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-pink-300 rounded-full opacity-10 animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto">
          <Card className="p-8 relative overflow-hidden bg-white/90 backdrop-blur-sm shadow-2xl border-0">
            {/* 动画背景 - Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-pink-50 to-rose-50 opacity-50">
              <div className="absolute inset-0">
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-3 h-3 bg-pink-300 rounded-full animate-pulse"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 3}s`,
                      animationDuration: `${3 + Math.random() * 2}s`,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* 主要内容 - Main content */}
            <div className="relative z-10">
              {/* 页面标题 - Page header */}
              <div className="text-center mb-8">
                <div className="flex items-center justify-center mb-6">
                  <div className="w-24 h-24 mx-auto bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center animate-pulse shadow-lg">
                    {generationSteps[currentStep] && (
                      <generationSteps[currentStep].icon className="w-12 h-12 text-white" />
                    )}
                  </div>
                </div>

                <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                  AI婚纱照生成中
                </h1>
                <p className="text-gray-600 mb-2">
                  {isGenerating ? "AI正在为您生成专业婚纱照..." : "准备开始生成"}
                </p>

                {/* 当前步骤显示 - Current step display */}
                {generationSteps[currentStep] && (
                  <div className="flex items-center justify-center mt-4">
                    <Badge className="bg-pink-100 text-pink-700 border-pink-200 px-4 py-2">
                      <generationSteps[currentStep].icon className="h-4 w-4 mr-2" />
                      {generationSteps[currentStep].name}
                    </Badge>
                  </div>
                )}
              </div>

              {/* 进度条 - Progress Bar */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-sm font-medium text-gray-700">生成进度</span>
                  <span className="text-sm font-medium text-pink-600">{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="w-full h-4 mb-4" />

                {/* 预估剩余时间 - Estimated remaining time */}
                {estimatedTimeRemaining > 0 && (
                  <div className="flex items-center justify-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-2" />
                    预估剩余时间: {Math.ceil(estimatedTimeRemaining / 60)} 分钟
                  </div>
                )}
              </div>

              {/* 当前风格和已完成风格 - Current style and completed styles */}
              {selectedStylesData && (
                <div className="mb-8 space-y-4">
                  {/* 当前处理的风格 - Currently processing style */}
                  {currentStyle && (
                    <div className="bg-pink-50 border border-pink-200 rounded-lg p-4">
                      <div className="flex items-center justify-center">
                        <Sparkles className="h-5 w-5 text-pink-500 mr-2 animate-spin" />
                        <span className="text-pink-700 font-medium">
                          正在生成: {selectedStylesData.styleDetails?.find((s: any) => s.id === currentStyle)?.name || currentStyle}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* 已完成的风格 - Completed styles */}
                  {completedStyles.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-700 flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                        已完成 ({completedStyles.length}/{selectedStylesData.selectedStyles.length})
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {completedStyles.map((styleId) => {
                          const styleData = selectedStylesData.styleDetails?.find((s: any) => s.id === styleId);
                          return (
                            <Badge key={styleId} className="bg-green-100 text-green-700 border-green-200">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              {styleData?.name || styleId}
                            </Badge>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 生成步骤 - Generation steps */}
              <div className="mb-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {generationSteps.map((step, index) => {
                    const StepIcon = step.icon;
                    return (
                      <div
                        key={step.id}
                        className={`flex items-center space-x-3 p-4 rounded-lg transition-all duration-300 ${
                          index === currentStep
                            ? "bg-pink-100 text-pink-700 border border-pink-200 shadow-md"
                            : index < currentStep
                            ? "bg-green-100 text-green-700 border border-green-200"
                            : "bg-gray-50 text-gray-500 border border-gray-200"
                        }`}
                      >
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            index === currentStep
                              ? "bg-pink-500 text-white animate-pulse"
                              : index < currentStep
                              ? "bg-green-500 text-white"
                              : "bg-gray-300 text-gray-600"
                          }`}
                        >
                          {index < currentStep ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <StepIcon className={`h-4 w-4 ${index === currentStep ? 'animate-pulse' : ''}`} />
                          )}
                        </div>
                        <div>
                          <span className="text-sm font-medium">{step.name}</span>
                          <div className="text-xs opacity-75">
                            {index === currentStep ? '进行中...' :
                             index < currentStep ? '已完成' : '等待中'}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 错误信息 - Error Message */}
              {error && (
                <div className="mb-6 p-6 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="text-red-700 font-medium mb-2">生成失败</p>
                      <p className="text-red-600 text-sm mb-4">{error}</p>
                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button
                          onClick={handleRetry}
                          className="bg-red-500 hover:bg-red-600 text-white flex items-center"
                          disabled={isGenerating}
                        >
                          <Sparkles className="h-4 w-4 mr-2" />
                          重新生成
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleCancel}
                          className="border-red-300 text-red-600 hover:bg-red-50"
                        >
                          <X className="h-4 w-4 mr-2" />
                          取消
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 操作按钮 - Action buttons */}
              {!error && (
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    className="border-gray-300 text-gray-600 hover:bg-gray-50 flex items-center"
                    disabled={isGenerating}
                  >
                    <X className="h-4 w-4 mr-2" />
                    取消生成
                  </Button>

                  {progress === 100 && (
                    <Button
                      className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white flex items-center"
                      onClick={() => {
                        const currentLocale = window.location.pathname.split('/')[1];
                        router.push(`/${currentLocale}/results`);
                      }}
                    >
                      <Heart className="h-4 w-4 mr-2" />
                      查看结果
                    </Button>
                  )}
                </div>
              )}

              {/* 生成提示 - Generation tips */}
              {!error && isGenerating && (
                <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start">
                    <Sparkles className="h-5 w-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="text-blue-800 font-medium mb-2">生成中请注意</h4>
                      <ul className="text-blue-700 text-sm space-y-1">
                        <li>• 请保持页面打开，生成过程需要几分钟时间</li>
                        <li>• AI正在为您精心制作每一张婚纱照</li>
                        <li>• 生成完成后将自动跳转到结果页面</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
